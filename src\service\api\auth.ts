import { request } from '../request';

// // 验证码
export const getCaptcha = () => request({ url: '/captcha', method: 'post' });

// google联合登录获取跳转地址
export const googleJumpUrl = (data: any) => request({ url: '/google_jump_url', method: 'post', data });

// Google线上回调地址-前端使用
export const googleCallBack = (data: any) => request({ url: '/auth/google/call_back', method: 'get', data });

// google线上回调-系统验证
export const googleJumpUrlLogin = (data: any) => request({ url: '/google_jump_url_login', method: 'post', data });

// 发送短信验证码
export const SendPhoneCode = (data: any) => request({ url: '/send_phone_code', method: 'post', data });

// 发送Email验证码
export const SendEmailCode = (data: any) => request({ url: '/send_email_code', method: 'post', data });

// 重置密码
export const ResetPwd = (data: any) => request({ url: '/user/reset_password', method: 'post', data });

// 修改Email号-验证
export const UpdateEmail = (data: any) => request({ url: '/user/update_email', method: 'post', data });

// 修改手机号-保存
export const UpdatePhone = (data: any) => request({ url: '/user/update_phone', method: 'post', data });

// 修改商户信息/账户昵称
export const UpdateOther = (data: any) => request({ url: '/user/update_other', method: 'post', data });

// 绑定优惠券-需要输入图形验证码
export const BindCoupon = (data: any) => request({ url: '/user/bind_coupon', method: 'post', data });

// 优惠券列表
export const CouponList = (data: any) => request({ url: '/user/my_coupon_list', method: 'post', data });

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(params: any) {
  return request<Api.Auth.LoginToken>({
    url: '/login_check',
    method: 'post',
    data: {
      ...params
    }
  });
}

// /user/owner_register
export function fetchRegister(params: any) {
  return request<Api.Auth.LoginToken>({
    url: '/user/owner_register',
    method: 'post',
    data: {
      ...params
    }
  });
}

/** Get user info */
export function fetchGetUserInfo(params: any = {}) {
  return request<Api.Auth.UserInfo>({ url: '/userindex', params });
}

// 获取日报周报查询
export const DailyData = (data: any) => request({ url: '/showdata_select', method: 'post', data });

// 获取国家
export const GetShops = (data: any) => request({ url: '/get_shops', method: 'post', data });

// 获取存在的店铺信息-单个店铺不同国家-激活信息
export const ActivedShopInfo = (data: any) => request({ url: '/user/actived_shop_info', method: 'post', data });

// 退出登录
export function fetchLogout() {
  return request<any>({ url: '/user_logout' });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}

// 删除 SP 授权
export const DelSPOuth = (data: any) =>
  request({
    url: '/amazon/del_sp_outh',
    method: 'post',
    data
  });

// 删除 AD 授权
export const DelADOuth = (data: any) =>
  request({
    url: '/amazon/del_ad_outh',
    method: 'post',
    data
  });

// SP 请求授权/二次授权
export const SPAuth = (data: any) =>
  request({
    url: '/amazon/sp_outh',
    method: 'post',
    data
  });

// AD 请求授权
export const ADAuth = (data: any) =>
  request({
    url: '/amazon/ad_outh',
    method: 'post',
    data
  });

// 获取授权数据列表
export const OuthList = () => request({ url: '/amazon/outh_list' });

// /user/shop_add
// 添加店铺
export const ShopAdd = (data: any) => request({ url: '/user/shop_add', method: 'post', data });

// /user/shop_change
// 更新店铺
export const ShopChange = (data: any) => request({ url: `/user/shop_change?ID=${data.ID}`, method: 'post', data });

// /user/shop_delete
// 删除店铺
export const ShopDelete = (data: any) => request({ url: '/user/shop_delete', method: 'post', data });

// 更新用户登录密码/changepwd
export const changePwd = (data: any) => request({ url: '/changepwd', method: 'post', data });

// 分组商户切换登录 /switch_user
export const switchUser = (data: any) => request({ url: '/switch_user', method: 'post', data });

// 第1步提交检查 url 或者asin /user/auth_asin_check
export const AuthAsinCheck = (data: any) => request({ url: '/user/auth_asin_check', method: 'post', data });

// 第二步提交asin 和 asin 预期acos和预计广告消费额 /user/auth_asin_add
export const AuthAsinAdd = (data: any) => request({ url: '/user/auth_asin_add', method: 'post', data });

// 获取修改 要更新的acos 和预期消费额  GET /user/auth_asin_update
export const AuthAsinGetUpdate = (data: any) => request({ url: '/user/set_shop_pre_acos', params: { ...data } });

// acos设置国家店铺/Asin预期acos五级（包括批量/单个Asin ）
export const AuthAsinSetShopPreAcos = (data: any) => request({ url: '/user/set_shop_pre_acos', method: 'post', data });

// 取消单个asin授权
export const AuthAsinCancel = (data: any) => request({ url: '/user/cancel_auth_asin', method: 'post', data });

// 暂停/恢复 Asin 运行
export const AuthAsinOperate = (data: any) => request({ url: '/user/auth_asin_operate', method: 'post', data });

// 获取单个国家授权列表
export const AuthAsinList = (data: any) => request({ url: '/user/asin_auth_list', method: 'post', data });

// 获取当前国家报告状态
export const GetReportState = (data: any) => request({ url: '/user/get_report_state', method: 'post', data });

// 请求免费报告
export const RequireReport = (data: any) => request({ url: '/user/require_report', method: 'post', data });

// 历史所有报告列表
export const ReportList = (data: any) => request({ url: '/user/report_list', method: 'post', data });

// Asin 操作日志-列表-可筛选
// POST /user/asin_op_log
export const AsinOpLog = (data: any) => request({ url: '/user/asin_op_log', method: 'post', data });

// 父asin添加/删除竞品子asin
// POST /user/comp_asin
export const CompAsin = (data: any) => request({ url: '/user/comp_asin', method: 'post', data });

// 查看竞品子asin
// POST /user/comp_asin_list
export const CompAsinList = (data: any) => request({ url: '/user/comp_asin_list', method: 'post', data });

// 获取某个店铺国家父级Asin下所有子Asin 状态
// POST /user/get_son_asin_ad_status
export const GetSonAsinAdStatus = (data: any) => request({ url: '/user/get_son_asin_ad_status', method: 'post', data });

// 打开/关闭子asin广告投放状态
// POST /usr/control_son_asin
export const ControlSonAsin = (data: any) => request({ url: '/user/set_son_asin_ad_status', method: 'post', data });

// 获取报告使用次数以及报告
// POST /user/get_count_report
export const ShowCountListing = (data: any) => request({ url: '/user/get_count_report', method: 'post', data });

// 添加报告记录并生成报告
// POST /user/create_report
export const CreateListingReport = (data: any) => request({ url: '/user/create_report', method: 'post', data });

// 分时预算设置接口
export function AuthAsinSetTimeBudget(data: { CountryCode: string; StartHour: number; EndHour: number }) {
  return request({ url: '/user/ad/low_budgets', method: 'post', data });
}

// 获取分时预算设置。不投放/低投放时间
export function AuthAsinGetLowBudgets(data: { CountryCode: string }) {
  return request({ url: '/user/ad/low_budgets', method: 'get', params: data });
}

// 新增投放/否定关键词
export function AuthAsinAddKeyWord(data: {
  PAsin: string;
  Keyword: Array<string>;
  CountryCode: string;
  KeywordType: string;
}) {
  return request({ url: '/user_shop_competitive_keyword/add_key_word', method: 'post', data });
}

// 删除投放/否定关键词
export function AuthAsinDeleteKeyWord(data: { ID: string }) {
  return request({ url: '/user_shop_competitive_keyword/delete_key_word', method: 'post', data });
}

// 分页筛选关键词和Asin记录
export function AuthAsinShowFilteredUserShopCompetitiveLog(data: { pagesize: number; page: number; where: object }) {
  return request({ url: '/user_shop_competitive_log/show_filtered_user_shop_competitive_log', method: 'post', data });
}

// 关键词 分页显示
export function AuthAsinShowFilteredKeyword(data: { pagesize: number; page: number; where: object }) {
  return request({ url: '/user_shop_competitive_keyword/show_filtered_keyword', method: 'post', data });
}
