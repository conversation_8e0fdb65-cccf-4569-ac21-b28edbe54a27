import { createSelector } from '@reduxjs/toolkit';
import { fetchGetUserInfo, fetchLogin, fetchRegister } from '@/service/api';
import { localStg } from '@/utils/storage';
import type { AppThunk } from '../../index';
import { createAppSlice } from '../../createAppSlice';
import { cacheTabs } from '../tab';
import { resetRouteStore } from '../route';
import { clearAuthStorage, getToken, getUserInfo } from './shared';

const initialState = {
  token: getToken(),
  userInfo: getUserInfo()
};

export const authSlice = createAppSlice({
  name: 'auth',
  initialState,
  reducers: create => ({
    login: create.asyncThunk(
      async (params: any) => {
        const api_type = params.register ? fetchRegister : fetchLogin;
        const { data: NickName, error } = await api_type({
          ...params
        });
        // 1. stored in the localStorage, the later requests need it in headers
        if (!error) {
          const randomString = Math.random().toString(36).substring(2, 20);
          localStg.set('token', randomString);
          localStg.set('refreshToken', randomString);

          const { data: info, error: userInfoError } = await fetchGetUserInfo();

          if (!userInfoError) {
            // 直接使用first_login字段，不需要额外的isFirstLogin
            const updatedInfo = {
              ...info,
              roles: info?.OwnerFlag === 0 ? ['R_ADMIN'] : ['R_USER_COMMON']
            };
            // 2. store user info
            localStg.set('userInfo', updatedInfo);
            return {
              token: randomString,
              userInfo: info
            };
          }
        } else {
          return {
            token: '',
            userInfo: {}
          };
        }

        return false;
      },

      {
        fulfilled: (state, { payload }) => {
          if (payload) {
            state.token = payload.token;
            state.userInfo = payload.userInfo;
          }
        }
      }
    ),
    resetAuth: create.reducer(() => initialState),
    updateUserInfo: create.reducer((state, { payload }) => {
      state.userInfo = payload.userInfo;
    }),
    clearUserInfo: create.reducer(state => {
      state.userInfo = {}; // or {} if you prefer an empty object
    })
  }),
  selectors: {
    selectToken: auth => auth.token,
    selectUserInfo: auth => {
      const userInfo = auth.userInfo;
      return {
        ...userInfo,
        roles: userInfo?.OwnerFlag == 0 ? ['R_ADMIN'] : ['R_USER_COMMON']
      };
    }
  }
});
export const { selectToken, selectUserInfo } = authSlice.selectors;
export const { login, resetAuth, updateUserInfo, clearUserInfo } = authSlice.actions;
// We can also write thunks by hand, which may contain both sync and async logic.
// Here's an example of conditionally dispatching actions based on current state.
export const getUerName = (): AppThunk<string> => (_, getState) => {
  const pass = selectToken(getState());

  return pass ? selectUserInfo(getState()).NickName : '';
};

/** is super role in static route */

export const isStaticSuper = (): AppThunk<boolean> => (_, getState) => {
  const { roles } = selectUserInfo(getState());
  // const roles =
  const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;
  return VITE_AUTH_ROUTE_MODE === 'static' && roles.includes(VITE_STATIC_SUPER_ROLE);
};

/** Reset auth store */
export const resetStore = (): AppThunk => dispatch => {
  clearAuthStorage();

  dispatch(clearUserInfo());

  dispatch(resetAuth());

  dispatch(resetRouteStore());

  dispatch(cacheTabs());
};

/** Is login */
export const getIsLogin = createSelector([selectToken], token => Boolean(token));
