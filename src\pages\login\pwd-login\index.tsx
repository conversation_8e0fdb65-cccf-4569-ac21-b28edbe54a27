import React, { useRef, useState } from 'react';
import { Button, Form, Input } from 'antd';
import { Icon } from '@iconify/react';
import { useLogin } from '@/hooks/common/login';
import { useGoogleLogin } from '@/hooks/common/useGoogleLogin';
import CaptchaQRCode from '../captch-qr-code';

type AccountKey = 'super' | 'admin' | 'user';
interface Account {
  key: AccountKey;
  label: string;
  email: string;
  password: string;
  captchaKey: string;
  captchaValue: string;
}
type LoginParams = Pick<Account, 'email' | 'password' | 'captchaKey' | 'captchaValue'>;

export function Component() {
  const [form] = Form.useForm<LoginParams>();
  const { toggleLoginModule } = useRouterPush();
  const { t } = useTranslation();
  const { loading, toLogin } = useLogin();
  const { loading: googleLoading, startGoogleLogin } = useGoogleLogin();
  const captchaRef = useRef(null);
  const [_searchParams] = useSearchParams();
  const [captchaKey, setCaptchaKey] = useState(''); // 保存验证码键
  const { formRules } = useFormRules();

  // Google登录处理函数
  const handleGoogleLogin = async () => {
    await startGoogleLogin();
  };

  async function handleSubmit() {
    const params = await form.validateFields();
    const isEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(params.email);
    const loginParams = isEmail
      ? { email: params.email, password: params.password, captchaValue: params.captchaValue, captchaKey, lang: 'zh' }
      : { phone: params.email, password: params.password, captchaValue: params.captchaValue, captchaKey, lang: 'zh' };
    toLogin(loginParams, captchaRef);
  }

  useKeyPress('enter', () => {
    handleSubmit();
  });

  // useEffect(() => {
  //   // 尝试打出 window.google 点出 accounts 时
  //   window.google.accounts.id.initialize({
  //     client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
  //     callback: (response: any) => {
  //       console.log(response.credential);
  //     }
  //   });
  // }, []);

  const handleCredentialResponse = (response: any) => {
    console.log(response);
  };

  return (
    <>
      <h1 className="mb-4 text-4xl font-semibold">{t('page.login.pwdLogin.title')}</h1>

      <div
        id="g_id_onload"
        data-client_id="*************-98o8pp66rol7ejt76vrn9dujq6dpbf02.apps.googleusercontent.com"
        data-callback={handleCredentialResponse}
      ></div>

      <div
        className="g_id_signin"
        data-type="standard"
        data-shape="rectangular"
        data-theme="outline"
        data-text="sign_in_with"
        data-size="large"
      ></div>

      <div className="mb-4 text-base leading-5">
        {t('page.login.pwdLogin.newToDeepBIAtlas')}
        <a
          className="ml-2 text-primary font-semibold"
          onClick={() => toggleLoginModule('register')}
        >
          {t('page.signup.title')}
        </a>
      </div>

      {/* Google登录按钮 */}
      <Button
        block
        size="large"
        loading={googleLoading}
        icon={
          !googleLoading && (
            <Icon
              icon="devicon:google"
              className="mr-2 text-xl"
            />
          )
        }
        onClick={handleGoogleLogin}
        className="mb-4 flex items-center justify-center border border-[#154EC1] bg-white hover:bg-gray-50"
      >
        <span className="text-primary font-medium">{t('page.login.pwdLogin.loginWithGoogle')}</span>
      </Button>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        initialValues={{
          email: '',
          password: ''
        }}
      >
        <Form.Item
          name="email"
          label={t('page.signup.email')}
          rules={[
            {
              validator: (_, value) => {
                if (!value) {
                  return Promise.reject(t('page.signup.emailPlaceholder'));
                }
                const isEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value);
                const isPhone = /^1[3-9]\d{9}$/.test(value);
                if (isEmail || isPhone) {
                  return Promise.resolve();
                }
                return Promise.reject(t('page.signup.emailPlaceholder'));
              }
            }
          ]}
          className="mb-4"
        >
          <Input
            placeholder={t('page.signup.emailPlaceholder')}
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={t('page.signup.password')}
          rules={formRules.pwd}
          className="mb-4"
        >
          <Input.Password
            autoComplete="password"
            placeholder={t('page.login.common.passwordPlaceholder')}
            size="large"
          />
        </Form.Item>

        {/* 图形验证码 */}
        <CaptchaQRCode
          ref={captchaRef}
          label={true}
          onCaptchaChange={(_value, key) => {
            setCaptchaKey(key);
          }}
        />

        <div className="mb-4 flex justify-end">
          <a
            className="text-primary font-semibold"
            onClick={() => toggleLoginModule('reset-pwd')}
          >
            {t('page.login.pwdLogin.forgetPassword')}
          </a>
        </div>

        <Button
          type="primary"
          size="large"
          block
          loading={loading}
          onClick={handleSubmit}
          className="mt-2"
        >
          {t('page.login.pwdLogin.title')}
        </Button>
      </Form>
    </>
  );
}
