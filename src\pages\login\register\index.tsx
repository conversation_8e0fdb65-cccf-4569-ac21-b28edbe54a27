import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Input, Space, Divider } from 'antd';
import { useLogin } from '@/hooks/common/login';
import { useGoogleLogin } from '@/hooks/common/useGoogleLogin';
import { Rule } from 'antd/es/form';
import CaptchaQRCode from '../captch-qr-code';
import { useCaptcha } from '@/hooks/business/captcha';
import { useFormRules } from '@/hooks/common/form';
import { Icon } from '@iconify/react';
export const Component = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { loading: toLoginLoading, toLogin } = useLogin();
  const { loading: googleLoading, startGoogleLogin } = useGoogleLogin();
  const { label, isCounting, loading, getCaptcha } = useCaptcha();
  const { formRules } = useFormRules();
  const captchaRef = useRef<any>(null);
  const [captchaKey, setCaptchaKey] = useState('');
  const { toggleLoginModule } = useRouterPush();


  // 发送邮箱验证码
  const getEmailCode = () => {
    form.validateFields(['email', 'captchaValue']).then(() => {
      const params = form.getFieldsValue();
      getCaptcha(form, { ...params, captchaKey }, (data: any) => {
        captchaRef.current.refreshCaptcha();
      });
    });
  };

  // Google登录处理函数
  const handleGoogleLogin = async () => {
    await startGoogleLogin();
  };

  async function handleSubmit() {
    const params = await form.validateFields();
    // 注册时带上邮箱、密码、验证码、邮箱验证码
    toLogin({ ...params, lang: 'zh', register: true, captchaKey }, captchaRef);
  }

  // 回车提交
  const handleEnterPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  // 添加键盘事件监听
  useEffect(() => {
    window.addEventListener('keydown', handleEnterPress);
    return () => {
      window.removeEventListener('keydown', handleEnterPress);
    };
  }, []);

  return (
    <>
      <h1 className="text-4xl font-semibold mb-4">{t('page.signup.title')}</h1>
      <div className="text-base mb-4 leading-5">
        {t('page.signup.haveAccount')}
        <a
          // type="link"
          className="font-semibold text-primary ml-2"
          onClick={() => toggleLoginModule('pwd-login')}
        >
          {t('page.signup.login')}
        </a>
      </div>


      {/* <Divider>
          <span className="text-gray-400 text-sm px-4">OR</span>
        </Divider> */}
        {/* 此处加入google登录 按钮*/}
        <Button
          block
          size="large"
          loading={googleLoading}
          icon={
            !googleLoading && <Icon icon="devicon:google" className="mr-2 text-xl" />
          }
          onClick={handleGoogleLogin}
          className="mb-4 flex items-center justify-center bg-white border border-[#154EC1] hover:bg-gray-50"
        >
          <span className="text-primary font-medium">{t('page.signup.signupWithGoogle')}</span>
        </Button>

      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="email"
          label={t('page.signup.email')}
          rules={formRules.email}
          className="mb-4"
        >
          <Input placeholder={t('page.signup.emailPlaceholder')} size="large" />
        </Form.Item>

        {/* 图形验证码 */}
        <CaptchaQRCode
          ref={captchaRef}
          label={true}
          onCaptchaChange={(value, key) => {
            setCaptchaKey(key);
          }}
        />

        <Form.Item
          label={t('page.login.codeLogin.title')}
          name="code"
          rules={formRules.phoneCode}
          className="mb-4"
        >
          <div className="w-full flex-y-center gap-4px">
            <Input placeholder={t('page.login.common.codePlaceholder')} size="large" />
            <Button
              disabled={isCounting}
              loading={loading}
              size="large"
              className='min-w-[116px]'
              onClick={getEmailCode}
            >
              {label}
            </Button>
          </div>
        </Form.Item>

        <Form.Item
          label={t('page.signup.password')}
          rules={formRules.pwd}
          name="password"
          className="mb-4"
        >
          <Input.Password
            placeholder={t('page.signup.passwordPlaceholder')}
            size="large"
          />
          <div className="text-xs text-gray-500 mt-1">
            {t('page.signup.passwordTip')}
          </div>
        </Form.Item>

        <Form.Item
          label={t('page.signup.repeatPassword')}
          name="confirmPassword"
          rules={[
            {
              required: true,
              message: t('page.signup.repeatPasswordRequired')
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t('page.signup.passwordMismatch')));
              }
            })
          ]}
          className="mb-4"
        >
          <Input.Password placeholder={t('page.signup.repeatPasswordPlaceholder')} size="large" />
        </Form.Item>

        <Button
          type="primary"
          size="large"
          block
          loading={toLoginLoading}
          onClick={handleSubmit}
          className="mt-4"
        >
          {t('page.signup.createAccount')}
        </Button>

      </Form>
    </>
  );
};

Component.displayName = 'Register';
