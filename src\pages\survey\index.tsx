import { useState } from 'react';
import { Avatar, Button, Dropdown, Form, Input, Select, Typography } from 'antd';
import { CheckCircleFilled, LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { useSubmit } from 'react-router-dom';
import { useAppDispatch } from '@/hooks/business/useStore';
import { resetStore, updateUserInfo } from '@/store/slice/auth';
import { localStg } from '@/utils/storage';
import procureSaleImg from '@/assets/imgs/procureSale.png';
import { fetchLogout } from '@/service/api/auth';
// Survey steps
const STEPS = [
  'welcome', // Step 0: Welcome page
  'about', // Step 1: What matters most to you?
  'complete' // Step 2: Completion
];

export function Component() {
  const { t } = useTranslation();
  const submit = useSubmit();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(1);
  const [showOtherRole, setShowOtherRole] = useState(false);
  const [showOtherSource, setShowOtherSource] = useState(false);
  const [showOtherInterests, setShowOtherInterests] = useState(false);
  const [showOtherTools, setShowOtherTools] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const userInfo: any = localStg.get('userInfo') || {};
  const countries = getCountryCode();
  const { toAuth } = useRouterPush();
  const handleLogout = () => {
    window?.$modal?.confirm({
      title: t('common.tip'),
      content: t('common.logoutConfirm'),
      okText: t('common.confirm'),
      cancelText: t('common.cancel'),
      onOk: () => {
        const needRedirect = false;
        // console.log(route.fullPath);
        fetchLogout().then(() => {
          dispatch(resetStore());
          submit({ redirectFullPath: '', needRedirect }, { method: 'post', action: '/logout' });
        });
        // return;
        //
      }
    });
  };

  const dropdownItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  const handleStart = () => {
    form
      .validateFields()
      .then(values => {
        console.log('Welcome page form data:', values);
        setCurrentStep(1); // Move to step 2 (about)
      })
      .catch(errorInfo => {
        console.log('Validation failed:', errorInfo);
      });
  };

  const handleNext = async () => {
    try {
      setIsSubmitting(true);
      // 模拟API请求
      await new Promise<void>(resolve => setTimeout(() => resolve(), 2000));
      const updatedUserInfo = { ...userInfo, first_login: 1 };
      localStg.set('userInfo', updatedUserInfo);
      dispatch(updateUserInfo({ userInfo: updatedUserInfo }));
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConnectAmazon = () => {
    // 这里可以添加连接Amazon店铺的逻辑
    console.log('连接Amazon店铺');
    toAuth();
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  // Render different form content based on current step
  const renderStepContent = () => {
    switch (STEPS[currentStep]) {
      case 'welcome':
        return (
          <div className="relative h-full flex justify-between overflow-hidden bg-layout">
            {/* 左侧内容区域 */}
            <div className="relative h-full w-2/3 flex flex-col bg-white">
              {/* 可滚动的内容区域 */}
              <div className="flex-1 overflow-auto p-8 pb-2">
                <div className="max-w-4xl w-full pl-8">
                  {/* 标题 */}
                  <Typography.Title
                    level={2}
                    className="mb-4 text-gray-800 font-bold"
                  >
                    {t('page.survey.welcome.title')}
                  </Typography.Title>

                  {/* 描述文字 */}
                  <Typography.Paragraph className="mb-8 text-lg text-gray-600 leading-relaxed">
                    {t('page.survey.welcome.description')}
                  </Typography.Paragraph>

                  {/* 表单卡片 */}
                  <div className="mt-12 overflow-hidden">
                    <Form
                      form={form}
                      layout="vertical"
                      requiredMark={true}
                      className="space-y-6"
                    >
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <Form.Item
                          name="firstName"
                          label={<span className="text-gray-700 font-medium">{t('page.survey.step1.firstName')}</span>}
                          rules={[{ required: true, message: t('page.survey.step1.firstNameRequired') }]}
                        >
                          <Input
                            placeholder={t('page.survey.step1.firstNamePlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>

                        <Form.Item
                          name="lastName"
                          label={<span className="text-gray-700 font-medium">{t('page.survey.step1.lastName')}</span>}
                          rules={[{ required: true, message: t('page.survey.step1.lastNameRequired') }]}
                        >
                          <Input
                            placeholder={t('page.survey.step1.lastNamePlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>
                      </div>

                      <Form.Item
                        name="companyName"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step1.companyName')}</span>}
                        rules={[{ required: true, message: t('page.survey.step1.companyNameRequired') }]}
                      >
                        <Input
                          placeholder={t('page.survey.step1.companyNamePlaceholder')}
                          size="large"
                          className="rounded-lg"
                        />
                      </Form.Item>

                      <Form.Item
                        name="role"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step1.role')}</span>}
                        rules={[{ required: true, message: t('page.survey.step1.roleRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step1.rolePlaceholder')}
                          onChange={value => setShowOtherRole(value === 'other')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="execute">{t('page.survey.step1.roleOptions.execute')}</Select.Option>
                          <Select.Option value="advise">{t('page.survey.step1.roleOptions.advise')}</Select.Option>
                          <Select.Option value="business">{t('page.survey.step1.roleOptions.business')}</Select.Option>
                          <Select.Option value="agency">{t('page.survey.step1.roleOptions.agency')}</Select.Option>
                          <Select.Option value="other">{t('page.survey.step1.roleOptions.other')}</Select.Option>
                        </Select>
                      </Form.Item>

                      {showOtherRole && (
                        <Form.Item
                          name="otherRole"
                          rules={[{ required: true, message: t('page.survey.step1.roleRequired') }]}
                        >
                          <Input
                            placeholder={t('page.survey.step1.otherRolePlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>
                      )}

                      <Form.Item
                        name="amazonBusiness"
                        label={
                          <span className="text-gray-700 font-medium">{t('page.survey.step1.amazonBusiness')}</span>
                        }
                        rules={[{ required: true, message: t('page.survey.step1.amazonBusinessRequired') }]}
                      >
                        <Select
                          mode="multiple"
                          placeholder={t('page.survey.step1.amazonBusinessPlaceholder')}
                          allowClear
                          size="large"
                          className="rounded-lg"
                          maxTagCount="responsive"
                        >
                          {countries.map((country: any) => (
                            <Select.Option
                              key={country.value}
                              value={country.value}
                              title={t(`page.setting.country.${country.value}`)}
                            >
                              <div className="flex items-center">
                                <Icon
                                  className="mr-2"
                                  icon={`circle-flags:${country.value.toLowerCase()}`}
                                  width={20}
                                  height={20}
                                />
                                <span>{t(`page.setting.country.${country.value}`)}</span>
                              </div>
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="hearAbout"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step1.hearAbout')}</span>}
                        rules={[{ required: true, message: t('page.survey.step1.hearAboutRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step1.hearAboutPlaceholder')}
                          onChange={value => setShowOtherSource(value === 'other')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="website">
                            {t('page.survey.step1.hearAboutOptions.website')}
                          </Select.Option>
                          <Select.Option value="google">{t('page.survey.step1.hearAboutOptions.google')}</Select.Option>
                          <Select.Option value="social">{t('page.survey.step1.hearAboutOptions.social')}</Select.Option>
                          <Select.Option value="amazon">{t('page.survey.step1.hearAboutOptions.amazon')}</Select.Option>
                          <Select.Option value="referral">
                            {t('page.survey.step1.hearAboutOptions.referral')}
                          </Select.Option>
                          <Select.Option value="event">{t('page.survey.step1.hearAboutOptions.event')}</Select.Option>
                          <Select.Option value="competitor">
                            {t('page.survey.step1.hearAboutOptions.competitor')}
                          </Select.Option>
                          <Select.Option value="other">{t('page.survey.step1.hearAboutOptions.other')}</Select.Option>
                        </Select>
                      </Form.Item>

                      {showOtherSource && (
                        <Form.Item
                          name="otherSource"
                          rules={[{ required: true, message: t('page.survey.step1.hearAboutRequired') }]}
                        >
                          <Input
                            placeholder={t('page.survey.step1.otherSourcePlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>
                      )}
                    </Form>
                  </div>
                </div>
              </div>

              {/* 固定在底部的按钮区域 */}
              <div className="h-20 flex items-center justify-end border-t border-b-#d9e0e8 bg-white px-8">
                <Button
                  type="primary"
                  size="large"
                  className="h-12 rounded-lg px-8 py-2 shadow-lg transition-all duration-200 hover:shadow-xl"
                  onClick={handleStart}
                >
                  {t('page.survey.welcome.nextButton')}
                </Button>
              </div>
            </div>

            {/* 右侧图片区域 */}
            <div className="relative h-full w-1/3 from-blue-400 to-blue-600 bg-gradient-to-br">
              <img
                src={procureSaleImg}
                alt="procure sale"
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        );

      case 'about':
        return (
          <div className="relative h-full flex justify-between overflow-hidden bg-layout">
            {/* 左侧内容区域 */}
            <div className="relative h-full w-2/3 flex flex-col bg-white">
              {/* 可滚动的内容区域 */}
              <div className="flex-1 overflow-auto p-8 pb-2">
                <div className="max-w-5xl w-full pl-8">
                  {/* 标题区域 */}
                  <div className="mb-4">
                    <Typography.Title
                      level={2}
                      className="mb-4 text-gray-800 font-bold"
                    >
                      {t('page.survey.step2.title')}
                    </Typography.Title>

                    <Typography.Paragraph className="text-lg text-gray-600 leading-relaxed">
                      {t('page.survey.step2.description')}
                    </Typography.Paragraph>
                  </div>

                  {/* 表单卡片 */}
                  <div className="overflow-hidden">
                    <Form
                      form={form}
                      layout="vertical"
                      requiredMark={true}
                      className="space-y-6"
                    >
                      <Form.Item
                        name="adExperience"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.adExperience')}</span>}
                        rules={[{ required: true, message: t('page.survey.step2.adExperienceRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step2.adExperiencePlaceholder')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="new">{t('page.survey.step2.adExperienceOptions.new')}</Select.Option>
                          <Select.Option value="some">{t('page.survey.step2.adExperienceOptions.some')}</Select.Option>
                          <Select.Option value="experienced">
                            {t('page.survey.step2.adExperienceOptions.experienced')}
                          </Select.Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="manageAds"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.manageAds')}</span>}
                        rules={[{ required: true, message: t('page.survey.step2.manageAdsRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step2.manageAdsPlaceholder')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="manual">{t('page.survey.step2.manageAdsOptions.manual')}</Select.Option>
                          <Select.Option value="automation">
                            {t('page.survey.step2.manageAdsOptions.automation')}
                          </Select.Option>
                          <Select.Option value="both">{t('page.survey.step2.manageAdsOptions.both')}</Select.Option>
                          <Select.Option value="notManaging">
                            {t('page.survey.step2.manageAdsOptions.notManaging')}
                          </Select.Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="interests"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.interests')}</span>}
                        rules={[{ required: true, message: t('page.survey.step2.interestsRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step2.interestsPlaceholder')}
                          size="large"
                          className="rounded-lg"
                          onChange={value => setShowOtherInterests(value === 'other')}
                        >
                          <Select.Option value="lowerCosts">
                            {t('page.survey.step2.interestsOptions.lowerCosts')}
                          </Select.Option>
                          <Select.Option value="increaseSales">
                            {t('page.survey.step2.interestsOptions.increaseSales')}
                          </Select.Option>
                          <Select.Option value="saveTime">
                            {t('page.survey.step2.interestsOptions.saveTime')}
                          </Select.Option>
                          <Select.Option value="newProducts">
                            {t('page.survey.step2.interestsOptions.newProducts')}
                          </Select.Option>
                          <Select.Option value="budgetControl">
                            {t('page.survey.step2.interestsOptions.budgetControl')}
                          </Select.Option>
                          <Select.Option value="other">{t('page.survey.step2.interestsOptions.other')}</Select.Option>
                        </Select>
                      </Form.Item>

                      {showOtherInterests && (
                        <Form.Item
                          name="otherInterests"
                          rules={[{ required: true, message: t('page.survey.step2.interestsRequired') }]}
                        >
                          <Input
                            placeholder={t('page.survey.step2.otherInterestsPlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>
                      )}

                      <Form.Item
                        name="storeCount"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.storeCount')}</span>}
                        rules={[{ required: true, message: t('page.survey.step2.storeCountRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step2.storeCountPlaceholder')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="one">{t('page.survey.step2.storeCountOptions.one')}</Select.Option>
                          <Select.Option value="twoThree">
                            {t('page.survey.step2.storeCountOptions.twoThree')}
                          </Select.Option>
                          <Select.Option value="fourFive">
                            {t('page.survey.step2.storeCountOptions.fourFive')}
                          </Select.Option>
                          <Select.Option value="moreThanFive">
                            {t('page.survey.step2.storeCountOptions.moreThanFive')}
                          </Select.Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="monthlySpend"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.monthlySpend')}</span>}
                        rules={[{ required: true, message: t('page.survey.step2.monthlySpendRequired') }]}
                      >
                        <Select
                          placeholder={t('page.survey.step2.monthlySpendPlaceholder')}
                          size="large"
                          className="rounded-lg"
                        >
                          <Select.Option value="lessThan1k">
                            {t('page.survey.step2.monthlySpendOptions.lessThan1k')}
                          </Select.Option>
                          <Select.Option value="oneToThree">
                            {t('page.survey.step2.monthlySpendOptions.oneToThree')}
                          </Select.Option>
                          <Select.Option value="threeToTen">
                            {t('page.survey.step2.monthlySpendOptions.threeToTen')}
                          </Select.Option>
                          <Select.Option value="moreThan10k">
                            {t('page.survey.step2.monthlySpendOptions.moreThan10k')}
                          </Select.Option>
                          <Select.Option value="notSure">
                            {t('page.survey.step2.monthlySpendOptions.notSure')}
                          </Select.Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="usedTools"
                        label={<span className="text-gray-700 font-medium">{t('page.survey.step2.usedTools')}</span>}
                      >
                        <Select
                          placeholder={t('page.survey.step2.usedToolsPlaceholder')}
                          size="large"
                          className="rounded-lg"
                          onChange={value => setShowOtherTools(value === 'other')}
                        >
                          <Select.Option value="helium10">
                            {t('page.survey.step2.usedToolsOptions.helium10')}
                          </Select.Option>
                          <Select.Option value="perpetua">
                            {t('page.survey.step2.usedToolsOptions.perpetua')}
                          </Select.Option>
                          <Select.Option value="quartile">
                            {t('page.survey.step2.usedToolsOptions.quartile')}
                          </Select.Option>
                          <Select.Option value="pacvue">{t('page.survey.step2.usedToolsOptions.pacvue')}</Select.Option>
                          <Select.Option value="other">{t('page.survey.step2.usedToolsOptions.other')}</Select.Option>
                        </Select>
                      </Form.Item>

                      {showOtherTools && (
                        <Form.Item name="otherTools">
                          <Input
                            placeholder={t('page.survey.step2.otherToolsPlaceholder')}
                            size="large"
                            className="rounded-lg"
                          />
                        </Form.Item>
                      )}
                    </Form>
                  </div>
                </div>
              </div>

              {/* 固定在底部的按钮区域 */}
              <div className="h-20 flex items-center justify-end border-t border-gray-100 bg-white px-8">
                <div className="flex items-center gap-4">
                  <Button
                    onClick={handlePrevious}
                    size="large"
                    disabled={isSubmitting}
                    className="h-12 rounded-lg px-6 py-2"
                  >
                    {t('page.survey.common.previous')}
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    className="h-12 rounded-lg px-8 py-2 shadow-lg transition-all duration-200 hover:shadow-xl"
                    onClick={handleNext}
                    loading={isSubmitting}
                  >
                    {t('page.survey.common.next')}
                  </Button>
                </div>
              </div>
            </div>

            {/* 右侧图片区域 */}
            <div className="relative h-full w-1/3 from-blue-400 to-blue-600 bg-gradient-to-br">
              <img
                src={procureSaleImg}
                alt="procure sale"
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        );

      case 'complete':
        return (
          <div className="min-h-screen flex items-center justify-center bg-#fff">
            <div className="max-w-3xl w-full">
              {/* 成功图标 */}
              <div className="mb-8 text-center">
                <div className="mb-6 h-24 w-24 inline-flex items-center justify-center rounded-full bg-green-100">
                  <CheckCircleFilled className="text-5xl text-green-500" />
                </div>

                {/* 标题 */}
                <Typography.Title
                  level={1}
                  className="mb-4 text-3xl text-gray-800 font-bold"
                >
                  🎉 {t('page.survey.complete.title')}
                </Typography.Title>

                {/* 描述 */}
                <Typography.Paragraph className="mx-auto mb-12 max-w-2xl text-xl text-gray-600 leading-relaxed">
                  {t('page.survey.complete.description')}
                </Typography.Paragraph>
              </div>

              {/* 功能特性展示 */}
              {/* <div className="grid mb-12 gap-6">
                <div className="border border-blue-100 rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
                  <div className="flex items-start">
                    <div className="mr-4 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-xl bg-blue-100">
                      <CheckCircleFilled className="text-xl text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="mb-2 text-lg text-gray-800 font-semibold">
                        {t('page.survey.complete.feature1.title')}
                      </h3>
                      <p className="text-base text-gray-600">{t('page.survey.complete.feature1.subtitle')}</p>
                    </div>
                  </div>
                </div>

                <div className="border border-green-100 rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
                  <div className="flex items-start">
                    <div className="mr-4 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-xl bg-green-100">
                      <CheckCircleFilled className="text-xl text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="mb-2 text-lg text-gray-800 font-semibold">
                        {t('page.survey.complete.feature2.title')}
                      </h3>
                      <p className="text-base text-gray-600">{t('page.survey.complete.feature2.subtitle')}</p>
                    </div>
                  </div>
                </div>

                <div className="border border-purple-100 rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
                  <div className="flex items-start">
                    <div className="mr-4 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-xl bg-purple-100">
                      <CheckCircleFilled className="text-xl text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="mb-2 text-lg text-gray-800 font-semibold">
                        {t('page.survey.complete.feature3.title')}
                      </h3>
                      <p className="text-base text-gray-600">{t('page.survey.complete.feature3.subtitle')}</p>
                    </div>
                  </div>
                </div>
              </div> */}

              {/* 行动按钮 */}
              <div className="text-center">
                <Button
                  type="primary"
                  size="large"
                  className="transform border-0 rounded-xl from-blue-600 to-indigo-600 bg-gradient-to-r px-12 py-3 text-lg font-semibold shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl"
                  onClick={handleConnectAmazon}
                  icon={<Icon icon="mdi:amazon" />}
                >
                  <span className="flex items-center">{t('page.survey.complete.connectButton')}</span>
                </Button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // If it's the welcome step, render it differently
  if (currentStep === 0) {
    return (
      <div
        className="flex flex-col bg-#f5f5f5"
        style={{ height: '100vh' }}
      >
        {/* Minimal header with just account info */}
        <div className="h-14 flex items-center justify-end border-1 border-b-#d9e0e8 bg-white px-4 shadow-sm">
          <Dropdown
            menu={{ items: dropdownItems }}
            placement="bottomRight"
          >
            <div className="flex cursor-pointer items-center">
              <Avatar
                icon={<UserOutlined />}
                className="mr-2 bg-primary"
              />
              <span className="mr-2">{userInfo.Email || 'User'}</span>
            </div>
          </Dropdown>
        </div>

        {/* Welcome page content */}
        <div style={{ height: 'calc(100vh - 56px)' }}>{renderStepContent()}</div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col bg-#f5f5f5"
      style={{ height: '100vh' }}
    >
      {/* Minimal header with just account info */}
      <div className="h-14 flex items-center justify-end border-1 border-b-#d9e0e8 bg-white px-4 shadow-sm">
        <Dropdown
          menu={{ items: dropdownItems }}
          placement="bottomRight"
        >
          <div className="flex cursor-pointer items-center">
            <Avatar
              icon={<UserOutlined />}
              className="mr-2 bg-primary"
            />
            <span className="mr-2">{userInfo.Email || 'User'}</span>
          </div>
        </Dropdown>
      </div>

      {/* Survey content - full screen like welcome */}
      <div style={{ height: 'calc(100vh - 56px)' }}>{renderStepContent()}</div>
    </div>
  );
}
