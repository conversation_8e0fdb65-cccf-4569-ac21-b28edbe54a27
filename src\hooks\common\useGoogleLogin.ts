import { useLoading } from '@sa/hooks';
import { useTranslation } from 'react-i18next';
import { googleCallBack, googleJumpUrl, googleJumpUrlLogin } from '@/service/api';
import { localStg } from '@/utils/storage';
import { initAuthRoute } from '@/store/slice/route';
import { useAppDispatch } from '../business/useStore';
import { useRouterPush } from './routerPush';

interface GoogleLoginResult {
  is_new: boolean;
  is_set_password: boolean;
  auth_token: string;
}

export function useGoogleLogin() {
  const { loading, startLoading, endLoading } = useLoading();
  const { t } = useTranslation();
  const { toSurvey, toAuth } = useRouterPush();
  const dispatch = useAppDispatch();

  /** 启动Google登录流程 */
  async function startGoogleLogin() {
    try {
      startLoading();

      // 1. 获取Google跳转地址
      const { data: jumpData, error: jumpError } = await googleJumpUrl({});

      if (jumpError || !jumpData?.jump_url) {
        window.$message?.error(t('page.login.pwdLogin.googleLoginFailedToGetUrl'));
        return false;
      }

      // 2. 打开Google登录窗口
      const popup = window.open(
        jumpData.jump_url,
        'google-login-popup',
        'width=1200,height=600,left=200,top=200,scrollbars=yes,resizable=yes'
      );

      if (!popup) {
        window.$message?.error(t('page.login.pwdLogin.googleLoginPopupBlocked'));
        return false;
      }

      // 3. 监听窗口关闭
      const result = await monitorPopupAndGetCode(popup);

      if (result) {
        // 4. 执行登录
        return await executeGoogleLogin(result.code);
      }

      return false;
    } catch (error) {
      console.error('Google登录过程中出现错误:', error);
      window.$message?.error(t('page.login.pwdLogin.googleLoginFailed'));
      return false;
    } finally {
      endLoading();
    }
  }

  /** 监听弹窗并获取code */
  async function monitorPopupAndGetCode(popup: Window): Promise<{ code: string } | null> {
    return new Promise(resolve => {
      // 监听窗口关闭
      const checkClosed = setInterval(async () => {
        if (popup.closed) {
          clearInterval(checkClosed);

          // 窗口关闭后查询回调接口获取code
          try {
            const { data: callbackData, error: callbackError } = await googleCallBack({});

            if (!callbackError && callbackData?.code) {
              resolve({ code: callbackData.code });
            } else {
              console.log('用户取消了Google登录或未获取到授权码');
              resolve(null);
            }
          } catch (error) {
            console.error('查询Google回调失败:', error);
            resolve(null);
          }
        }
      }, 1000);

      // 设置超时时间（5分钟）
      setTimeout(
        () => {
          clearInterval(checkClosed);
          if (!popup.closed) {
            popup.close();
          }
          console.log('Google登录超时');
          resolve(null);
        },
        1 * 60 * 1000
      );
    });
  }

  /** 执行Google登录 */
  async function executeGoogleLogin(code: string): Promise<boolean> {
    try {
      // 调用登录接口
      const { data: loginData, error: loginError } = await googleJumpUrlLogin({ code });

      if (loginError || !loginData) {
        window.$message?.error(t('page.login.pwdLogin.googleLoginVerificationFailed'));
        return false;
      }

      const result = loginData as GoogleLoginResult;

      // 存储token
      if (result.auth_token) {
        localStg.set('token', result.auth_token);
        localStg.set('refreshToken', result.auth_token);
      }

      // 初始化路由
      await dispatch(initAuthRoute());

      // 显示成功消息
      window.$notification?.success({
        message: t('page.login.common.loginSuccess'),
        description: t('page.login.common.welcomeBack')
      });

      // 根据is_new判断跳转
      if (result.is_new) {
        // 新用户跳转到survey页面
        await toSurvey();
      } else {
        // 老用户跳转到主页面
        await toAuth();
      }

      return true;
    } catch (error) {
      console.error('Google登录执行失败:', error);
      window.$message?.error(t('page.login.pwdLogin.googleLoginProcessFailed'));
      return false;
    }
  }

  return {
    loading,
    startGoogleLogin
  };
}
